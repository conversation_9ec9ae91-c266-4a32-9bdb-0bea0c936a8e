import React, { useState, useEffect } from 'react';
import { View, Text, StyleSheet, ScrollView, TouchableOpacity, Alert, Image } from 'react-native';
import { useSelector, useDispatch } from 'react-redux';
import { useNavigation } from '@react-navigation/native';
import { RootState } from '../../store/store';
import { logout } from '../../store/slices/authSlice';
import { googleAuthService } from '../../services/googleAuth';
import { apiService, LedgerStatsResponse } from '../../services/apiService';
import { TransactionType, TransactionStatus } from '../../types/Transaction';
import { Colors } from '../../constants/colors';
import { formatCurrency } from '../../utils/currency';
import { canCreateTransaction, canApproveTransactions } from '../../utils/permissions';
import Button from '../../components/common/Button';
import TransactionCard from '../../components/cards/TransactionCard';

export default function DashboardScreen() {
  const navigation = useNavigation<any>();
  const dispatch = useDispatch();
  const { user, googleUser } = useSelector((state: RootState) => state.auth);
  const { transactions } = useSelector((state: RootState) => state.transactions);

  // State for ledger stats
  const [ledgerStats, setLedgerStats] = useState<LedgerStatsResponse | null>(null);
  const [statsLoading, setStatsLoading] = useState(true);
  const [statsError, setStatsError] = useState<string | null>(null);

  if (!user) return null;

  // Fetch ledger stats from API
  useEffect(() => {
    const fetchLedgerStats = async () => {
      try {
        setStatsLoading(true);
        setStatsError(null);
        const stats = await apiService.getLedgerStats();
        setLedgerStats(stats);
      } catch (error) {
        console.error('Error fetching ledger stats:', error);
        setStatsError('Failed to load statistics');
      } finally {
        setStatsLoading(false);
      }
    };

    fetchLedgerStats();
  }, []);

  // Filter transactions for current user or all if advisor/admin
  const userTransactions = canApproveTransactions(user.role, user.isAdvisor)
    ? transactions
    : transactions.filter(t => t.createdBy === user.id);

  // Calculate statistics from API data
  const totalReceived = ledgerStats ? parseFloat(ledgerStats.total_received) : 0;
  const totalSpent = ledgerStats ? parseFloat(ledgerStats.total_spent) : 0;
  const currentBalance = totalReceived - totalSpent;

  const pendingCount = userTransactions
    .filter(t => t.status === TransactionStatus.PENDING).length;

  const recentTransactions = [...userTransactions]
    .sort((a, b) => new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime())
    .slice(0, 3);

  const handleLogout = async () => {
    Alert.alert(
      'Logout',
      'Are you sure you want to logout?',
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Logout',
          onPress: async () => {
            try {
              await googleAuthService.signOut();
              dispatch(logout());
            } catch (error) {
              console.error('Logout error:', error);
              dispatch(logout()); // Logout anyway
            }
          }
        },
      ]
    );
  };

  const navigateToCreateTransaction = (type: TransactionType) => {
    navigation.navigate('CreateTransaction', { type });
  };

  const navigateToApprovals = () => {
    navigation.navigate('ApprovalList');
  };

  const navigateToTransactions = () => {
    navigation.navigate('TransactionList');
  };

  return (
    <ScrollView style={styles.container} showsVerticalScrollIndicator={false}>
      {/* Header */}
      <View style={styles.header}>
        <View style={styles.userInfo}>
          {googleUser?.photo && (
            <Image
              source={{ uri: googleUser.photo }}
              style={styles.profileImage}
            />
          )}
          <View style={styles.userText}>
            <Text style={styles.greeting}>Hello, {user.name.split(' ')[0]}!</Text>
            <Text style={styles.role}>
              {user.role.charAt(0).toUpperCase() + user.role.slice(1)}
              {user.isAdvisor && ' • Advisor'}
            </Text>
            {googleUser?.email && (
              <Text style={styles.email}>{googleUser.email}</Text>
            )}
          </View>
        </View>
        <TouchableOpacity onPress={handleLogout} style={styles.logoutButton}>
          <Text style={styles.logoutText}>Logout</Text>
        </TouchableOpacity>
      </View>

      {/* Statistics Cards */}
      <View style={styles.statsContainer}>
        <View style={styles.statCard}>
          <Text style={[styles.statValue, { color: currentBalance >= 0 ? Colors.success : Colors.error }]}>
            {statsLoading ? '...' : formatCurrency(currentBalance)}
          </Text>
          <Text style={styles.statLabel}>Current Balance</Text>
        </View>
        <View style={styles.statCard}>
          <Text style={styles.statValue}>
            {statsLoading ? '...' : formatCurrency(totalReceived)}
          </Text>
          <Text style={styles.statLabel}>Cash Given</Text>
        </View>
        <View style={styles.statCard}>
          <Text style={[styles.statValue, { color: Colors.error }]}>
            {statsLoading ? '...' : formatCurrency(totalSpent)}
          </Text>
          <Text style={styles.statLabel}>Cash Spent</Text>
        </View>
      </View>

      {/* View My Transactions */}
      <View style={styles.sectionHeader}>
        <TouchableOpacity onPress={navigateToTransactions}>
          <Text style={styles.seeAllText}>View My Transactions</Text>
        </TouchableOpacity>
      </View>

      {/* Quick Actions */}
      <View style={styles.section}>
        <Text style={styles.sectionTitle}>Quick Actions</Text>
        <View style={styles.actionGrid}>
          {canCreateTransaction(user.role, TransactionType.SPEND) && (
            <TouchableOpacity
              style={styles.actionCard}
              onPress={() => navigateToCreateTransaction(TransactionType.SPEND)}
            >
              <Text style={styles.actionIcon}>💳</Text>
              <Text style={styles.actionTitle}>Log Cash Spent</Text>
              <Text style={styles.actionDescription}>Record expenses and purchases</Text>
            </TouchableOpacity>
          )}

          {canCreateTransaction(user.role, TransactionType.TRANSFER) && (
            <TouchableOpacity
              style={styles.actionCard}
              onPress={() => navigateToCreateTransaction(TransactionType.TRANSFER)}
            >
              <Text style={styles.actionIcon}>💰</Text>
              <Text style={styles.actionTitle}>Log Cash Given</Text>
              <Text style={styles.actionDescription}>Give cash to student or staff</Text>
            </TouchableOpacity>
          )}

          {canCreateTransaction(user.role, TransactionType.RETURNED) && (
            <TouchableOpacity
              style={styles.actionCard}
              onPress={() => navigateToCreateTransaction(TransactionType.RETURNED)}
            >
              <Text style={styles.actionIcon}>↩️</Text>
              <Text style={styles.actionTitle}>Log Cash Returned</Text>
              <Text style={styles.actionDescription}>Return unused cash to school</Text>
            </TouchableOpacity>
          )}

          {canApproveTransactions(user.role, user.isAdvisor) && (
            <TouchableOpacity
              style={styles.actionCard}
              onPress={navigateToApprovals}
            >
              <Text style={styles.actionIcon}>✅</Text>
              <Text style={styles.actionTitle}>Pending Approval</Text>
              <Text style={styles.actionDescription}>Approve/reject student transactions</Text>
            </TouchableOpacity>
          )}
        </View>
      </View>

      {/* Recent Transactions */}
      <View style={styles.section}>
        {recentTransactions.length > 0 ? (
          recentTransactions.map((transaction) => (
            <TransactionCard
              key={transaction.id}
              transaction={transaction}
              onPress={() => navigation.navigate('TransactionDetail', {
                transactionId: transaction.id
              })}
              showUser={canApproveTransactions(user.role, user.isAdvisor)}
            />
          ))
        ) : (
          <View style={styles.emptyState}>
            <Text style={styles.emptyText}>No transactions yet</Text>
            <Text style={styles.emptySubtext}>Create your first transaction to get started</Text>
          </View>
        )}
      </View>
    </ScrollView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.surface,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 20,
    paddingTop: 60,
    backgroundColor: Colors.background,
  },
  userInfo: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  profileImage: {
    width: 50,
    height: 50,
    borderRadius: 25,
    marginRight: 12,
  },
  userText: {
    flex: 1,
  },
  greeting: {
    fontSize: 24,
    fontWeight: 'bold',
    color: Colors.textPrimary,
  },
  role: {
    fontSize: 14,
    color: Colors.textSecondary,
    marginTop: 2,
  },
  email: {
    fontSize: 12,
    color: Colors.textTertiary,
    marginTop: 2,
  },
  logoutButton: {
    padding: 8,
  },
  logoutText: {
    color: Colors.primary,
    fontSize: 16,
    fontWeight: '600',
  },
  statsContainer: {
    flexDirection: 'row',
    padding: 16,
    gap: 12,
  },
  statCard: {
    flex: 1,
    backgroundColor: Colors.background,
    padding: 16,
    borderRadius: 12,
    alignItems: 'center',
  },
  statValue: {
    fontSize: 20,
    fontWeight: 'bold',
    color: Colors.success,
  },
  statLabel: {
    fontSize: 12,
    color: Colors.textSecondary,
    marginTop: 4,
  },
  section: {
    marginTop: 8,
  },
  sectionHeader: {
    flexDirection: 'row',
    justifyContent: 'flex-end',
    alignItems: 'center',
    paddingHorizontal: 20,
    marginBottom: 16,
  },
  sectionTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: Colors.textPrimary,
    paddingHorizontal: 20,
    marginBottom: 16,
  },
  seeAllText: {
    color: Colors.primary,
    fontSize: 16,
    fontWeight: '600',
  },
  actionGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    paddingHorizontal: 16,
    gap: 12,
  },
  actionCard: {
    width: '47%',
    backgroundColor: Colors.background,
    padding: 20,
    borderRadius: 12,
    alignItems: 'center',
  },
  actionIcon: {
    fontSize: 32,
    marginBottom: 8,
  },
  actionTitle: {
    fontSize: 14,
    fontWeight: '600',
    color: Colors.textPrimary,
    textAlign: 'center',
    marginBottom: 4,
  },
  actionDescription: {
    fontSize: 12,
    color: Colors.textSecondary,
    textAlign: 'center',
  },
  emptyState: {
    alignItems: 'center',
    padding: 40,
  },
  emptyText: {
    fontSize: 18,
    fontWeight: '600',
    color: Colors.textSecondary,
  },
  emptySubtext: {
    fontSize: 14,
    color: Colors.textTertiary,
    marginTop: 8,
    textAlign: 'center',
  },
  errorContainer: {
    backgroundColor: Colors.error + '20',
    padding: 12,
    marginHorizontal: 16,
    marginBottom: 16,
    borderRadius: 8,
    borderWidth: 1,
    borderColor: Colors.error + '40',
  },
  errorText: {
    color: Colors.error,
    fontSize: 14,
    textAlign: 'center',
  },
});
