import Constants from 'expo-constants';

interface AppConfig {
  apiBaseUrl: string;
  googleWebClientId: string;
  googleIosClientId: string;
  googleAndroidClientId: string;
  appEnv: string;
  debugMode: boolean;
}

// Get configuration from Expo Constants (which reads from app.config.js)
const getConfig = (): AppConfig => {
  const extra = Constants.expoConfig?.extra || {};
  
  return {
    apiBaseUrl: extra.apiBaseUrl || 'https://tgswallet-backend.onrender.com',
    googleWebClientId: extra.googleWebClientId || 'your-web-client-id.googleusercontent.com',
    googleIosClientId: extra.googleIosClientId || 'your-ios-client-id.googleusercontent.com',
    googleAndroidClientId: extra.googleAndroidClientId || 'your-android-client-id.googleusercontent.com',
    appEnv: extra.appEnv || 'development',
    debugMode: extra.debugMode === 'true' || extra.debugMode === true || __DEV__,
  };
};

export const config = getConfig();

// API Endpoints
export const API_ENDPOINTS = {
  GOOGLE_AUTH: `${config.apiBaseUrl}/users/auth/google/`,
  REFRESH_TOKEN: `${config.apiBaseUrl}/users/auth/refresh/`,
  CATEGORIES: `${config.apiBaseUrl}/categories/`,
  USERS: `${config.apiBaseUrl}/users/`,
  TRANSACTIONS: `${config.apiBaseUrl}/transactions/`,
  LEDGER_STATS: `${config.apiBaseUrl}/ledgers/stats/`,
} as const;

// Storage Keys
export const STORAGE_KEYS = {
  ACCESS_TOKEN: 'access_token',
  REFRESH_TOKEN: 'refresh_token',
  USER_DATA: 'user_data',
  GOOGLE_USER_DATA: 'google_user_data',
} as const;

// Debug logging
export const debugLog = (message: string, data?: any) => {
  if (config.debugMode) {
    console.log(`[THINK Wallet Debug] ${message}`, data || '');
  }
};
