import React, { useState } from 'react';
import { View, Text, StyleSheet, Alert, TouchableOpacity } from 'react-native';
import { useSelector, useDispatch } from 'react-redux';
import { useNavigation, useRoute } from '@react-navigation/native';
import uuid from 'react-native-uuid';
import { RootState } from '../../store/store';
import { addTransaction, updateTransaction } from '../../store/slices/transactionSlice';
import { TransactionType, TransactionStatus } from '../../types/Transaction';
import { Colors } from '../../constants/colors';
import { canCreateTransaction } from '../../utils/permissions';
import TransactionForm from '../../components/forms/TransactionForm';
import TransactionPreviewModal from '../../components/modals/TransactionPreviewModal';
import { apiService, TransactionCreateRequest } from '../../services/apiService';

export default function CreateTransactionScreen() {
  const navigation = useNavigation();
  const route = useRoute<any>();
  const dispatch = useDispatch();
  const { user } = useSelector((state: RootState) => state.auth);
  const [loading, setLoading] = useState(false);
  const selectedType = route.params?.type || TransactionType.SPEND

  if (!user) return null;

  const handleSubmit = async (data: any) => {
    setLoading(true);
    try {
      const transactionRequest: TransactionCreateRequest = {
        submit: true,
        type: selectedType,
        amount: data.amount,
        currency: 'USD',
        notes: data.description,
        category: data.programCode,
        receipt_uri: data.receiptUri,
        recipient: data.recipient,
        split_with: data.splitWith || [],
      };

      const response = await apiService.createTransaction(transactionRequest);

      // Also update local state for immediate UI feedback
      const localTransaction = {
        id: response.id,
        type: response.type,
        status: response.status,
        amount: response.amount,
        currency: response.currency,
        description: response.description,
        purpose: response.purpose,
        glCode: response.gl_code,
        programCode: response.program_code,
        receiptUri: response.receipt_uri,
        createdBy: user.id,
        recipient: response.recipient,
        splitWith: response.split_with,
        createdAt: new Date(response.created_at),
        updatedAt: new Date(response.updated_at),
      };

      dispatch(addTransaction(localTransaction));

      Alert.alert(
        'Success',
        'Transaction submitted successfully',
        [{ text: 'OK', onPress: () => navigation.goBack() }]
      );
    } catch (error) {
      console.error('Transaction submission error:', error);
      Alert.alert('Error', 'Failed to create transaction. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  const handleSaveDraft = async (data: any) => {
    setLoading(true);
    try {
      const transactionRequest: TransactionCreateRequest = {
        submit: false,
        type: selectedType,
        amount: data.amount,
        currency: 'USD',
        notes: data.description,
        category: data.programCode,
        receipt_uri: data.receiptUri,
        split_with: data.splitWith || [],
      };

      const response = await apiService.createTransaction(transactionRequest);

      // Also update local state for immediate UI feedback
      const localTransaction = {
        id: response.id,
        type: response.type,
        status: response.status,
        amount: response.amount,
        currency: response.currency,
        description: response.description,
        purpose: response.purpose,
        glCode: response.gl_code,
        programCode: response.program_code,
        receiptUri: response.receipt_uri,
        createdBy: user.id,
        recipient: response.recipient,
        splitWith: response.split_with,
        createdAt: new Date(response.created_at),
        updatedAt: new Date(response.updated_at),
      };

      dispatch(addTransaction(localTransaction));

      Alert.alert(
        'Draft Saved',
        'Transaction saved as draft',
        [{ text: 'OK', onPress: () => navigation.goBack() }]
      );
    } catch (error) {
      console.error('Draft save error:', error);
      Alert.alert('Error', 'Failed to save draft. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  const getFormTitle = () => {
    switch (selectedType) {
      case TransactionType.SPEND:
        return 'Log Cash Spent';
      case TransactionType.TRANSFER:
        return 'Log Cash Given';
      case TransactionType.RETURNED:
        return 'Log Cash Returned';
      default:
        return 'New Transaction';
    }
  };

  return (
    <View style={styles.container}>
      {/* Header */}
      <View style={styles.header}>
        <TouchableOpacity onPress={() => navigation.goBack()}>
          <Text style={styles.backButton}>← Back</Text>
        </TouchableOpacity>
        <Text style={styles.title}>{getFormTitle()}</Text>
        <View style={styles.placeholder} />
      </View>

      {/* Transaction Form */}
      <TransactionForm
        type={selectedType}
        onSubmit={handleSubmit}
        onSaveDraft={handleSaveDraft}
        loading={loading}
      />
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.surface,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 20,
    paddingTop: 60,
    backgroundColor: Colors.background,
  },
  backButton: {
    color: Colors.primary,
    fontSize: 16,
    fontWeight: '600',
  },
  title: {
    fontSize: 18,
    fontWeight: 'bold',
    color: Colors.textPrimary,
  },
  placeholder: {
    width: 50,
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 40,
  },
  errorTitle: {
    fontSize: 24,
    fontWeight: 'bold',
    color: Colors.textPrimary,
    marginBottom: 16,
  },
  errorMessage: {
    fontSize: 16,
    color: Colors.textSecondary,
    textAlign: 'center',
  },
});
