import React, { useState, useMemo, useEffect, useId } from 'react';
import { View, Text, TouchableOpacity, StyleSheet, ScrollView, TextInput } from 'react-native';
import { Controller } from 'react-hook-form';
import { Colors } from '../../constants/colors';
import { mockUsers } from '../../store/mockData/users';
import { User, UserRole } from '../../types/User';
import { useDropdownContext } from '../../contexts/DropdownContext';
import { apiService } from '../../services/apiService';

interface UserPickerProps {
  control: any;
  name: string;
  label: string;
  placeholder: string;
  error?: string;
  multiple?: boolean; // Legacy prop for backward compatibility
  multiplePick?: boolean; // New prop name as requested
  userFilter?: (user: User) => boolean;
  fetchUsers?: () => Promise<User[]>;
  required?: boolean;
}

export default function UserPicker({
  control,
  name,
  label,
  placeholder,
  error,
  multiple = false,
  multiplePick,
  userFilter,
  fetchUsers,
  required = false,
}: UserPickerProps) {
  // Use multiplePick if provided, otherwise fall back to multiple for backward compatibility
  const isMultiple = multiplePick !== undefined ? multiplePick : multiple;
  const [isOpen, setIsOpen] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');
  const [users, setUsers] = useState<User[]>([]);
  const [loading, setLoading] = useState(true);
  const dropdownManager = useDropdownContext();
  const dropdownId = useId();

  // Filter users based on search query and optional filter
  const filteredUsers = useMemo(() => {
    let filtered = users;

    // Apply custom filter if provided
    if (userFilter) {
      filtered = filtered.filter(userFilter);
    }

    // Apply search query - only search by email
    if (searchQuery.trim()) {
      const query = searchQuery.toLowerCase().trim();
      filtered = filtered.filter(user =>
        user.email.toLowerCase().includes(query)
      );
    }

    return filtered;
  }, [users, searchQuery, userFilter]);

  useEffect(() => {
    dropdownManager.registerDropdown(dropdownId, () => setIsOpen(false));
    return () => dropdownManager.unregisterDropdown(dropdownId);
  }, [dropdownManager, dropdownId]);

  useEffect(() => {
    const loadUsers = async () => {
      try {
        setLoading(true);
        let fetchedUsers: User[];
        
        if (fetchUsers) {
          fetchedUsers = await fetchUsers();
        } else {
          // Default fallback to mock data
          fetchedUsers = mockUsers;
        }
        
        setUsers(fetchedUsers);
      } catch (error) {
        console.error('Failed to fetch users:', error);
        // Fallback to mock data if API fails
        setUsers(mockUsers);
      } finally {
        setLoading(false);
      }
    };

    loadUsers();
  }, [fetchUsers]);

  const handleToggle = () => {
    if (!isOpen) {
      dropdownManager.closeAllExcept(dropdownId);
      setSearchQuery(''); // Reset search when opening
    }
    setIsOpen(!isOpen);
  };

  const renderUserItem = (
    user: User,
    selectedValue: string | string[],
    onToggleUser: (userId: string) => void
  ) => {
    const isSelected = isMultiple
      ? Array.isArray(selectedValue) && selectedValue.includes(user.id)
      : selectedValue === user.id;

    return (
      <TouchableOpacity
        key={user.id}
        style={styles.dropdownItem}
        onPress={() => onToggleUser(user.id)}
      >
        <View style={styles.userContainer}>
          {isMultiple && (
            <View style={[styles.checkbox, isSelected && styles.checkboxSelected]}>
              {isSelected && <Text style={styles.checkmark}>✓</Text>}
            </View>
          )}
          <View style={styles.userInfo}>
            <Text style={styles.userName}>
              {user.email}
            </Text>
          </View>
        </View>
      </TouchableOpacity>
    );
  };

  const getDisplayText = (selectedValue: string | string[]) => {
    if (isMultiple) {
      const selectedUsers = Array.isArray(selectedValue) ? selectedValue : [];
      if (selectedUsers.length === 0) {
        return placeholder;
      }

      const selectedEmails = selectedUsers
        .map(id => {
          const user = users.find(u => u.id === id);
          return user ? user.email : null;
        })
        .filter(Boolean);

      if (selectedEmails.length === 0) {
        return 'Selected users';
      }

      // For multiple users, show each on a new line for better readability
      if (selectedEmails.length > 1) {
        return selectedEmails.join('\n');
      }

      return selectedEmails[0];
    } else {
      if (!selectedValue) {
        return placeholder;
      }

      const selectedUser = users.find(u => u.id === selectedValue);
      return selectedUser ? selectedUser.email : placeholder;
    }
  };

  return (
    <Controller
      control={control}
      name={name}
      defaultValue={isMultiple ? [] : ''}
      render={({ field: { onChange, value }, fieldState: { error: fieldError } }) => {
        const selectedValue = value || (isMultiple ? [] : '');

        const toggleUser = (userId: string) => {
          if (isMultiple) {
            const currentSelection = Array.isArray(selectedValue) ? selectedValue : [];
            const newSelection = currentSelection.includes(userId)
              ? currentSelection.filter((id: string) => id !== userId)
              : [...currentSelection, userId];
            onChange(newSelection);
          } else {
            onChange(selectedValue === userId ? '' : userId);
            setIsOpen(false); // Close dropdown after single selection
          }
        };

        const hasSelection = isMultiple
          ? Array.isArray(selectedValue) && selectedValue.length > 0
          : Boolean(selectedValue);

        return (
          <View style={styles.container}>
            <Text style={styles.label}>
              {label} {required && <Text style={styles.required}>*</Text>}
            </Text>

            <TouchableOpacity
              style={[
                styles.picker,
                hasSelection && styles.pickerWithContent,
                (error || fieldError) && styles.pickerError,
                isOpen && styles.pickerOpen,
              ]}
              onPress={handleToggle}
            >
              <Text style={[
                styles.pickerText,
                !hasSelection && styles.placeholder,
                hasSelection && styles.selectedText,
              ]}>
                {getDisplayText(selectedValue)}
              </Text>
              <Text style={[styles.arrow, isOpen && styles.arrowOpen]}>▼</Text>
            </TouchableOpacity>

            {isOpen && (
              <View style={styles.dropdown}>
                <View style={styles.searchContainer}>
                  <TextInput
                    style={styles.searchInput}
                    placeholder="Search by email..."
                    placeholderTextColor={Colors.gray400}
                    value={searchQuery}
                    onChangeText={setSearchQuery}
                    autoCapitalize="none"
                    autoCorrect={false}
                  />
                </View>
                <ScrollView
                  style={styles.dropdownList}
                  showsVerticalScrollIndicator={false}
                  nestedScrollEnabled={true}
                >
                  {loading ? (
                    <View style={styles.loadingContainer}>
                      <Text style={styles.loadingText}>Loading users...</Text>
                    </View>
                  ) : filteredUsers.length > 0 ? (
                    filteredUsers.map(user =>
                      renderUserItem(user, selectedValue, toggleUser)
                    )
                  ) : (
                    <View style={styles.noResultsContainer}>
                      <Text style={styles.noResultsText}>
                        {searchQuery ? `No users found matching "${searchQuery}"` : 'No users available'}
                      </Text>
                    </View>
                  )}
                </ScrollView>
              </View>
            )}

            {(error || fieldError) && (
              <Text style={styles.errorText}>{error || fieldError?.message}</Text>
            )}
          </View>
        );
      }}
    />
  );
}

const styles = StyleSheet.create({
  container: {
    marginVertical: 8,
    zIndex: 999,
    position: 'relative',
  },
  label: {
    fontSize: 16,
    fontWeight: '600',
    color: Colors.textPrimary,
    marginBottom: 8,
  },
  required: {
    color: Colors.error,
  },
  picker: {
    borderWidth: 1,
    borderColor: Colors.gray300,
    borderRadius: 8,
    paddingHorizontal: 16,
    paddingVertical: 12,
    backgroundColor: Colors.background,
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    minHeight: 48,
  },
  pickerWithContent: {
    alignItems: 'flex-start',
    paddingVertical: 16,
  },
  pickerError: {
    borderColor: Colors.error,
  },
  pickerOpen: {
    borderBottomLeftRadius: 0,
    borderBottomRightRadius: 0,
    borderBottomColor: Colors.gray200,
  },
  pickerText: {
    fontSize: 16,
    color: Colors.textPrimary,
    flex: 1,
    lineHeight: 22,
  },
  selectedText: {
    lineHeight: 22,
    flexWrap: 'wrap',
  },
  placeholder: {
    color: Colors.gray400,
  },
  arrow: {
    fontSize: 12,
    color: Colors.gray400,
    marginLeft: 8,
    marginTop: 2,
  },
  arrowOpen: {
    transform: [{ rotate: '180deg' }],
  },
  dropdown: {
    position: 'absolute',
    top: '100%',
    left: 0,
    right: 0,
    borderWidth: 1,
    borderTopWidth: 0,
    borderColor: Colors.gray300,
    borderBottomLeftRadius: 8,
    borderBottomRightRadius: 8,
    backgroundColor: Colors.background,
    maxHeight: 250,
    zIndex: 1000,
    elevation: 5, // For Android shadow
    shadowColor: '#000', // For iOS shadow
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
  },
  searchContainer: {
    padding: 12,
    borderBottomWidth: 1,
    borderBottomColor: Colors.gray200,
  },
  searchInput: {
    borderWidth: 1,
    borderColor: Colors.gray300,
    borderRadius: 6,
    paddingHorizontal: 12,
    paddingVertical: 8,
    fontSize: 16,
    color: Colors.textPrimary,
    backgroundColor: Colors.background,
  },
  dropdownList: {
    maxHeight: 180, // Reduced to account for search input
  },
  noResultsContainer: {
    padding: 20,
    alignItems: 'center',
  },
  noResultsText: {
    fontSize: 16,
    color: Colors.gray400,
    textAlign: 'center',
  },
  dropdownItem: {
    padding: 12,
    borderBottomWidth: 1,
    borderBottomColor: Colors.gray100,
  },
  userContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  checkbox: {
    width: 20,
    height: 20,
    borderWidth: 2,
    borderColor: Colors.gray300,
    borderRadius: 4,
    marginRight: 12,
    alignItems: 'center',
    justifyContent: 'center',
  },
  checkboxSelected: {
    backgroundColor: Colors.primary,
    borderColor: Colors.primary,
  },
  checkmark: {
    color: Colors.background,
    fontSize: 12,
    fontWeight: 'bold',
  },
  userInfo: {
    flex: 1,
  },
  userName: {
    fontSize: 16,
    fontWeight: '600',
    color: Colors.textPrimary,
  },
  userEmail: {
    fontSize: 14,
    color: Colors.textSecondary,
    marginTop: 2,
  },
  userRole: {
    fontSize: 12,
    color: Colors.gray400,
    marginTop: 2,
  },
  userClass: {
    fontSize: 12,
    color: Colors.gray500,
    marginTop: 1,
    fontStyle: 'italic',
  },
  errorText: {
    color: Colors.error,
    fontSize: 14,
    marginTop: 4,
  },
  loadingContainer: {
    padding: 20,
    alignItems: 'center',
  },
  loadingText: {
    fontSize: 16,
    color: Colors.gray400,
  },
});
